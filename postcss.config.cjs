const path = require('path');

const hexToRgba = (hex, alpha = 1) => {
  hex = hex.replace(/^#|["']/g, '');

  if (!/^[0-9a-fA-F]{6}$/.test(hex)) {
    return `rgba(${hex}, ${alpha})`;
  }

  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

module.exports = {
  plugins: [
    require('postcss-import')({
      path: [path.relative(process.cwd(), '../')],
    }),
    require('postcss-nesting'),
    require('./postcss-breakpoints.cjs'),
    require('postcss-custom-media'),
    require('postcss-combine-duplicated-selectors'),
    require('postcss-discard-empty'),
    require('./postcss-whitespace.cjs'),
    require('postcss-functions')({
      functions: {
        hexToRgba,
      },
    }),
    require('autoprefixer'),
  ],
};
