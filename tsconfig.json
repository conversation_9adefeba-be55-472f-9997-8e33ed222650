{"compilerOptions": {"module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "lib": ["DOM", "ESNext", "DOM.Iterable"], "jsx": "react-jsx", "jsxImportSource": "react", "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "outDir": "dist", "strict": true, "skipLibCheck": true, "esModuleInterop": true, "verbatimModuleSyntax": true, "allowSyntheticDefaultImports": true, "paths": {"@base-components": ["./src/base-components"], "@base-components/*": ["./src/base-components/*"], "@components": ["./src/components"], "@components/*": ["./src/components/*"], "@helpers": ["./src/helpers"], "@helpers/*": ["./src/helpers/*"], "@props": ["./src/props"], "@props/*": ["./src/props/*"]}}}