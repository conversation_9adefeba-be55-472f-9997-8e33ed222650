{"$schema": "http://json.schemastore.org/prettierrc", "useTabs": false, "tabWidth": 2, "printWidth": 120, "semi": true, "singleQuote": true, "arrowParens": "always", "trailingComma": "all", "endOfLine": "auto", "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["<THIRD_PARTY_MODULES>", "^@herond", "^@(assets|base-components|components|helpers|props|styles|utils)", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "overrides": [{"files": "*.css", "options": {"printWidth": 120}}]}