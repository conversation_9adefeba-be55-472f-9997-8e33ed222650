import type { <PERSON>a, StoryObj } from '@storybook/react-vite';

import { type MenuItem, Sidebar, collapsible, side, types, variants } from '@components/sidebar';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'DesignSystem/Sidebar',
  component: Sidebar,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    type: {
      control: 'inline-radio',
      options: types,
    },
    variant: {
      control: 'inline-radio',
      options: variants,
    },
    side: {
      control: 'inline-radio',
      options: side,
    },
    collapsible: {
      control: 'inline-radio',
      options: collapsible,
    },
  },
} satisfies Meta<typeof Sidebar>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockData: Array<MenuItem> = [
  {
    title: 'Home',
    path: '#',
    icon: 'home',
    type: 'group',
    children: [
      {
        title: 'Submenu 1',
        path: '#',
        icon: 'home',
      },
      {
        title: 'Submenu 2',
        path: '#',
        icon: 'home',
        children: [
          {
            title: 'Submenu 3',
            path: '#',
            icon: 'home',
          },

          {
            title: 'Submenu 5',
            path: '#',
            icon: 'home',
            children: [
              {
                title: 'Submenu 6',
                path: '#',
                icon: 'home',
              },
            ],
          },
        ],
      },
      {
        title: 'Submenu 4',
        path: '#',
        icon: 'home',
      },
    ],
  },
  {
    title: 'Group2',
    path: '#',
    type: 'group',
    icon: 'dashboard',
    children: [
      {
        title: 'Submenu 1',
        path: '#',
        icon: 'home',
      },
      {
        title: 'Submenu 2',
        path: '#',
        icon: 'home',
      },
    ],
  },
  {
    title: 'Dashboard',
    path: '#',
    icon: 'dashboard',
  },
  {
    title: 'Settings',
    path: '#',
    icon: 'settings',
  },
];

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const _sidebar: Story = {
  args: {
    data: mockData,
  },
};
