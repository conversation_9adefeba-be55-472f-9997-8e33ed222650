import clsx from 'clsx';
import { Collapsible } from 'radix-ui';
import React, { type JSX, type ReactNode } from 'react';

import { Flex } from '@components/flex';
import { SidebarGroup, SidebarGroupLabel } from '@components/sidebar/sidebar-group';
import { SidebarContent, SidebarHeader } from '@components/sidebar/sidebar-layout';
import { SidebarMenu, SidebarMenuItem, SidebarMenuSubItem } from '@components/sidebar/sidebar-menu';
import { SidebarProvider, useSidebar } from '@components/sidebar/sidebar-provider';
import { sidebarMenuItemPropDefs, sidebarPropDefs } from '@components/sidebar/sidebar.props';
import type { MenuItem } from '@components/sidebar/type';
import { extractProps } from '@helpers';
import type { GetPropDefTypes } from '@props';

type SideBarContainerProps = React.ComponentProps<'div'> & GetPropDefTypes<typeof sidebarPropDefs>;

const SidebarContainer: React.FC<SideBarContainerProps> = (p): JSX.Element => {
  const { state } = useSidebar();
  const { className, children, ...props } = extractProps(p, sidebarPropDefs);

  if (p.collapsible === 'none') {
    return (
      <Flex
        data-slot="sidebar"
        direction={'column'}
        width={'var(--sidebar-width)'}
        className={clsx('rt-sidebar', className)}
        {...props}
      >
        {children}
      </Flex>
    );
  }

  return (
    <div
      className="rt-sidebar"
      data-state={state}
      data-collapsible={state === 'collapsed' ? p.collapsible : ''}
      data-type={p.type}
      data-side={p.side}
      data-slot="sidebar"
    >
      {/* This is what handles the sidebar gap on desktop */}
      <div data-slot="sidebar-gap" className={clsx('rt-sidebar-gap', p.type)} />
      <div data-slot="sidebar-container" className={clsx('rt-sidebar-container', p.side, p.type, className)} {...props}>
        <div data-sidebar="sidebar" data-slot="sidebar-inner" className="rt-sidebar-inner">
          {children}
        </div>
      </div>
    </div>
  );
};

type SidebarProps = GetPropDefTypes<typeof sidebarMenuItemPropDefs> &
  GetPropDefTypes<typeof sidebarPropDefs> & {
    header?: ReactNode;
    data: MenuItem[];
  };

const Sidebar: React.FC<SidebarProps> = ({ header, data, ...props }) => {
  const menuItemProps = extractProps(props, sidebarMenuItemPropDefs);

  const renderGroup = (group: MenuItem, children: MenuItem[], level: number) => {
    return (
      <SidebarGroup key={group.title}>
        <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
        <SidebarMenu>{renderMenu(children, level)}</SidebarMenu>
      </SidebarGroup>
    );
  };

  const renderMenu = (menus: MenuItem[], level = 0) => {
    return menus.map((item) => {
      if (item.type === 'group') {
        return renderGroup(item, item.children ?? [], level + 1);
      }

      if (item.children) {        
        return (
          <Collapsible.Root asChild defaultOpen={item.isActive} key={item.title}>
            <SidebarMenuItem {...menuItemProps}>
              <Collapsible.Trigger>
                <SidebarMenuSubItem>{item.title}</SidebarMenuSubItem>
              </Collapsible.Trigger>
              <SidebarMenu>
                <Collapsible.Content>{renderMenu(item.children, level + 1)}</Collapsible.Content>
              </SidebarMenu>
            </SidebarMenuItem>
          </Collapsible.Root>
        );
      }

      const subItem = (
        <SidebarMenuSubItem key={item.title}>
          <span>{item.title}</span>
        </SidebarMenuSubItem>
      );
      return level <= 1 ? <SidebarMenuItem key={item.title}>{subItem}</SidebarMenuItem> : subItem;
    });
  };
  return (
    <SidebarProvider>
      <SidebarContainer {...props}>
        <SidebarHeader>{header}</SidebarHeader>
        <SidebarContent>{renderMenu(data)}</SidebarContent>
      </SidebarContainer>
    </SidebarProvider>
  );
};

export { Sidebar };

export type { SidebarProps };
