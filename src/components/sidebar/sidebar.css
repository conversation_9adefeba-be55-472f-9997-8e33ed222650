.rt-sidebar-wrapper {
    --sidebar-width: 16rem;
    --sidebar-width-icon: 3rem;
}

.rt-sidebar:where([data-collapsible="offcanvas"]) .rt-sidebar-gap {
    width: 0;
}

.rt-sidebar:where([data-side="right"]) .rt-sidebar-gap {
    transform: rotate(180deg)
}

.rt-sidebar[data-collapsible="icon"] .rt-sidebar-gap.rt-type-floating,
.rt-sidebar[data-collapsible="icon"] .rt-sidebar-gap.rt-type-inset {
    width: calc(var(--sidebar-width-icon) + var(--spacing-4));
}

.rt-sidebar[data-collapsible="icon"] .rt-sidebar-gap:not(.rt-inset):not(.rt-type-floating) {
    width: var(--sidebar-width-icon);
}

.rt-sidebar:where(.rt-collapsible-none) {
    background-color: var(--background-grouped-primary-high);
}

.rt-sidebar .rt-sidebar-container .rt-sidebar-inner {
    background-color: var(--background-grouped-primary-high);
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.rt-sidebar-container {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 10;
    display: none;
    height: 100svh;
    width: var(--sidebar-width);
    transition: left 200ms linear, right 200ms linear, width 200ms linear;
}

@media (min-width: 768px) {
    .rt-sidebar-container {
        display: flex;
    }
}

.rt-sidebar-container .rt-side-left {
    left: 0;
}

.rt-sidebar-container .rt-side-right {
    right: 0;
}

.rt-sidebar-container:where(.rt-type-floating),
.rt-sidebar-container:where(.rt-type-inset) {
    padding: var(--spatial-2);
}



.rt-sidebar-group-label {
    display: flex;
    height: 2rem;
    flex-shrink: 0;
    align-items: center;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: rgb(var(--sidebar-foreground) / 0.7);
    outline: none;
    transition: margin 200ms linear, opacity 200ms linear;
}

.rt-sidebar-group-label:focus-visible {
    box-shadow: 0 0 0 2px var(--sidebar-ring);
}

/* SVG child styling */
.rt-sidebar-group-label>svg {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
}

/*
Sidebar menu
*/


.rt-sidebar-menu {
    display: flex;
    flex-direction: column;
    gap: var(--spatial-2);
    width: 100%;
}


.rt-sidebar-menu-item {
    position: relative;


}


.rt-sidebar-menu-sub {
    display: flex;
    flex-direction: column;
    transform: translateX(var(--spatial-1));
    gap: var(--spatial-1);
}

.rt-sidebar-menu-sub-item {
    position: relative;
}