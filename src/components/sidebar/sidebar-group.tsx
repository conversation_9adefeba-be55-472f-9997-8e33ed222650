import clsx from 'clsx';
import { Slot } from 'radix-ui';

import { Flex } from '@components/flex';

const SidebarGroup: React.FC<React.ComponentProps<'div'>> = ({ className, ...props }) => {
  return (
    <Flex
      data-slot="sidebar-group"
      data-sidebar="group"
      width={'100%'}
      direction={'column'}
      minWidth={'0'}
      p={'2'}
      {...props}
    />
  );
};

const SidebarGroupLabel: React.FC<React.ComponentProps<'div'> & { asChild?: boolean }> = ({
  className,
  asChild = false,
  ...props
}) => {
  const Comp = asChild ? Slot.Root : 'div';

  return (
    <Comp
      data-slot="sidebar-group-label"
      data-sidebar="group-label"
      className={clsx('rt-sidebar-group-label', className)}
      {...props}
    />
  );
};

export { SidebarGroup, SidebarGroupLabel };
