import { type PropDef } from '@props';

const types = ['sidebar', 'floating', 'inset'] as const;
const side = ['left', 'right'] as const;
const collapsible = ['offcanvas', 'icon', 'none'] as const;

const sidebarPropDefs = {
  type: { type: 'enum', className: 'rt-type', values: types, default: 'sidebar' },
  side: { type: 'enum', className: 'rt-side', values: side, default: 'left' },
  collapsible: { type: 'enum', className: 'rt-collapsible', values: collapsible, default: 'none' },
} satisfies {
  type: PropDef<(typeof types)[number]>;
  side: PropDef<(typeof side)[number]>;
  collapsible: PropDef<(typeof collapsible)[number]>;
};

const variants = ['active-glass', 'inactive', 'active-primary', 'active-primary-accent', 'active-fill'] as const;
const sizes = ['1', '2', '3'] as const;

const sidebarMenuItemPropDefs = {
  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'active-fill' },
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2' },
} satisfies {
  variant: PropDef<(typeof variants)[number]>;
  size: PropDef<(typeof sizes)[number]>;
};

export { sidebarPropDefs, types, side, collapsible, sidebarMenuItemPropDefs, variants, sizes };
