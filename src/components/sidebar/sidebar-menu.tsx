import clsx from 'clsx';
import type React from 'react';
import type { ReactNode } from 'react';

import { useSidebar } from '@components/sidebar/sidebar-provider';
import { Tooltip } from '@components/tooltip';

const SidebarMenu: React.FC<React.ComponentProps<'ul'>> = ({ className, ...props }) => {
  return <ul data-slot="sidebar-menu" data-sidebar="menu" className={clsx('rt-sidebar-menu', className)} {...props} />;
};

const SidebarMenuItem: React.FC<React.ComponentProps<'li'>> = ({ className, ...props }) => {
  return (
    <li
      data-slot="sidebar-menu-item"
      data-sidebar="menu-item"
      className={clsx('rt-sidebar-menu-item', className)}
      {...props}
    />
  );
};

const SidebarMenuSubItem: React.FC<React.ComponentProps<'div'> & { tooltip?: string | ReactNode }> = ({
  className,
  tooltip,
  ...props
}) => {
  const { state } = useSidebar();
  const subItem = (
    <div
      data-slot="sidebar-menu-sub-item"
      data-sidebar="menu-sub-item"
      className={clsx('rt-sidebar-menu-sub-item', className)}
      {...props}
    />
  );

  if (!tooltip) {
    return subItem;
  }

  return (
    <Tooltip content={tooltip} hidden={state !== 'collapsed'} side="right" align="center">
      {subItem}
    </Tooltip>
  );
};

export { SidebarMenu, SidebarMenuItem, SidebarMenuSubItem };
