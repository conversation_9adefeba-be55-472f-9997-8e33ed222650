import clsx from 'clsx';
import { Tooltip } from 'radix-ui';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { Flex } from '@components/flex';

const SIDEBAR_COOKIE_NAME = 'sidebar_state';
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7; // 7 days
const SIDEBAR_KEYBOARD_SHORTCUT = 'b';

type SidebarContextProps = {
  state: 'expanded' | 'collapsed';
  open: boolean;
  setOpen: (open: boolean) => void;
  toggleSidebar: () => void;
};

const SidebarContext = createContext<SidebarContextProps | null>(null);

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider.');
  }

  return context;
}

type Props = React.ComponentProps<'div'> & {
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
};

export const SidebarProvider: React.FC<Props> = ({
  defaultOpen = true,
  open: openProp,
  onOpenChange,
  className,
  style,
  children,
  ...props
}) => {
  const [open, setOpen] = useState(defaultOpen);
  const isOpen = openProp ?? open;

  const handleOpen = useCallback(
    (value: boolean | ((value: boolean) => boolean)) => {
      const openState = typeof value === 'function' ? value(isOpen) : value;
      if (onOpenChange) {
        onOpenChange(openState);
      } else {
        setOpen(openState);
      }

      // This sets the cookie to keep the sidebar state.
      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;
    },
    [onOpenChange, isOpen],
  );

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {
        event.preventDefault();
        handleOpen((open) => !open);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleOpen]);

  // We add a state so that we can do data-state="expanded" or "collapsed".
  // This makes it easier to style the sidebar with Tailwind classes.
  const state = isOpen ? 'expanded' : 'collapsed';

  const contextValue = useMemo<SidebarContextProps>(
    () => ({
      state,
      open: isOpen,
      setOpen,
      toggleSidebar: () => handleOpen((open) => !open),
    }),
    [state, isOpen, setOpen, handleOpen],
  );

  return (
    <SidebarContext.Provider value={contextValue}>
      <Tooltip.Provider delayDuration={0}>
        <Flex
          data-slot="sidebar-wrapper"
          minHeight="100svh"
          width={'100%'}
          className={clsx('rt-sidebar-wrapper', className)}
          {...props}
        >
          {children}
        </Flex>
      </Tooltip.Provider>
    </SidebarContext.Provider>
  );
};
