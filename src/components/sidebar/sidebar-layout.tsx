import { Flex } from '@components/flex';

const SidebarHeader: React.FC<React.ComponentProps<'div'>> = (props) => {
  return <Flex data-slot="sidebar-header" data-sidebar="header" gap={'2'} direction={'column'} p={'2'} {...props} />;
};

const SidebarContent: React.FC<React.ComponentProps<'div'>> = ({ className, ...props }) => {
  return (
    <Flex
      data-slot="sidebar-content"
      data-sidebar="content"
      direction={'column'}
      overflow={'auto'}
      gap={'2'}
      flexBasis={'0'}
      flexGrow={'1'}
      flexShrink={'1'}
      {...props}
    />
  );
};

export { SidebarContent, SidebarHeader };
