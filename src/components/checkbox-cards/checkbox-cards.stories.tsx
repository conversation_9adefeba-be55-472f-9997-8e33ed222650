import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';

import { Box } from '@components/box';
import * as BaseCheckboxCards from '@components/checkbox-cards';
import { Flex } from '@components/flex';
import { Text } from '@components/text';

const CheckboxCards = (props: BaseCheckboxCards.RootProps) => {
  return (
    <Box maxWidth="600px">
      <BaseCheckboxCards.Root {...props} defaultValue={['1']} columns={{ initial: '1', sm: '3' }}>
        <BaseCheckboxCards.Item value="1">
          <Flex direction="column" width="100%">
            <Text weight="bold">A1 Keyboard</Text>
            <Text>US Layout</Text>
          </Flex>
        </BaseCheckboxCards.Item>
        <BaseCheckboxCards.Item value="2">
          <Flex direction="column" width="100%">
            <Text weight="bold">Pro Mouse</Text>
            <Text>Zero-lag wireless</Text>
          </Flex>
        </BaseCheckboxCards.Item>
        <BaseCheckboxCards.Item value="3">
          <Flex direction="column" width="100%">
            <Text weight="bold">Lightning Mat</Text>
            <Text>Wireless charging</Text>
          </Flex>
        </BaseCheckboxCards.Item>
      </BaseCheckboxCards.Root>
    </Box>
  );
};

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'DesignSystem/CheckboxCards',
  component: CheckboxCards,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    variant: {
      control: 'inline-radio',
      options: ['square', 'sphere'],
    },
    size: { control: 'inline-radio', options: ['1', '2', '3', '4', '5'] },
    highContrast: { control: 'boolean' },
    disabled: { control: 'boolean' },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: { onClick: fn() },
} satisfies Meta<typeof CheckboxCards>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const _checkbox_cards: Story = {
  args: {
    variant: 'square',
    size: '3',
    highContrast: false,
    disabled: false,
  },
};
