import clsx from 'clsx';
import { Slot } from 'radix-ui';
import * as React from 'react';

import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs, textPropDefs } from '@props';

type TextElement = React.ComponentRef<'span'>;
type TextOwnProps = GetPropDefTypes<typeof textPropDefs>;
interface CommonTextProps extends MarginProps, TextOwnProps {}
type TextSpanProps = { as?: 'span' } & ComponentPropsWithout<'span', RemovedProps>;
type TextDivProps = { as: 'div' } & ComponentPropsWithout<'div', RemovedProps>;
type TextLabelProps = { as: 'label' } & ComponentPropsWithout<'label', RemovedProps>;
type TextPProps = { as: 'p' } & ComponentPropsWithout<'p', RemovedProps>;
type TextProps = CommonTextProps & (TextSpanProps | TextDivProps | TextLabelProps | TextPProps);

const Text = React.forwardRef<TextElement, TextProps>((props, forwardedRef) => {
  const {
    children,
    className,
    asChild,
    as: Tag = 'span',
    ...textProps
  } = extractProps(props, textPropDefs, marginPropDefs);
  return (
    <Slot.Root {...textProps} ref={forwardedRef} className={clsx('rt-Text', className)}>
      {asChild ? children : <Tag>{children}</Tag>}
    </Slot.Root>
  );
});
Text.displayName = 'Text';

export { Text };
export type { TextProps };
