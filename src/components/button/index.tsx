import clsx from 'clsx';
import * as React from 'react';

import { BaseButton } from '@base-components/base-button';
import Icon, { type IconSize, type IconType } from '@components/icon';

type ButtonElement = React.ComponentRef<typeof BaseButton>;
interface ButtonProps extends Omit<React.ComponentPropsWithoutRef<typeof BaseButton>, 'prefix'> {
  suffix?: IconType;
  prefix?: IconType;
  icon?: IconType;
  iconSize?: IconSize;
  label?: string;
}
const Button = React.forwardRef<ButtonElement, ButtonProps>(
  ({ className, prefix, suffix, icon, iconSize, label, ...props }, forwardedRef) => {
    const children = icon ? (
      <Icon icon={icon} size={iconSize} />
    ) : prefix && suffix ? (
      [<Icon icon={prefix} size={iconSize} />, label, <Icon icon={suffix} size={iconSize} />]
    ) : prefix ? (
      [<Icon icon={prefix} size={iconSize} />, label]
    ) : suffix ? (
      [label, <Icon icon={suffix} size={iconSize} />]
    ) : (
      label
    );

    return (
      <BaseButton
        {...props}
        ref={forwardedRef}
        className={clsx('rt-Button', className)}
        children={props.children ?? children}
      />
    );
  },
);
Button.displayName = 'Button';

export { Button };
export type { ButtonProps };
