import type { Preview } from '@storybook/react-vite';
import { useGlobals } from 'storybook/internal/preview-api';

import { Theme } from '../src/components';
import '../src/styles/index.css';
import './preview.css';

const preview: Preview = {
  parameters: {
    docs: {
      codePanel: true,
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => {
      const [global] = useGlobals();
      return (
        <Theme appearance={global?.backgrounds?.value ?? 'light'}>
          <Story />
        </Theme>
      );
    },
  ],
};

export default preview;
