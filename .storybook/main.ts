import type { StorybookConfig } from '@storybook/react-vite';

import postcssStorybookConfig from '../postcss.storybook.config.cjs';

const config: StorybookConfig = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: ['@storybook/addon-docs'],
  framework: {
    name: '@storybook/react-vite',
    options: {},
  },
  viteFinal: async (config) => {
    config.css = {
      ...config.css,
      postcss: postcssStorybookConfig,
    };
    return config;
  },
};
export default config;
